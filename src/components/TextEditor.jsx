'use client';

import React, { useState, useEffect, useRef } from 'react';

const TextEditor = ({
  value = '',
  onChange,
  placeholder = 'Start typing...',
  className = '',
  style = {},
  disabled = false,
  minHeight = '200px'
}) => {
  const [content, setContent] = useState(value);
  const [selectedText, setSelectedText] = useState('');
  const [currentFont, setCurrentFont] = useState('Arial');
  const [currentColor, setCurrentColor] = useState('#000000');
  const [currentFontSize, setCurrentFontSize] = useState('40px');
  const editorRef = useRef(null);
  const isUpdatingRef = useRef(false);

  // Initialize content when component mounts
  useEffect(() => {
    if (editorRef.current && !editorRef.current.innerHTML) {
      editorRef.current.innerHTML = value || '';
      setContent(value || '');
    }
  }, []);

  // Update content when value prop changes (but not during user editing)
  useEffect(() => {
    if (value !== content && !isUpdatingRef.current && editorRef.current) {
      setContent(value);
      // Only update innerHTML if it's actually different to avoid cursor issues
      if (editorRef.current.innerHTML !== value) {
        const selection = window.getSelection();
        const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
        const cursorPosition = range ? range.startOffset : 0;

        editorRef.current.innerHTML = value;

        // Try to restore cursor position
        if (range && editorRef.current.firstChild) {
          try {
            const newRange = document.createRange();
            const textNode = editorRef.current.firstChild;
            const maxOffset = textNode.textContent ? Math.min(cursorPosition, textNode.textContent.length) : 0;
            newRange.setStart(textNode, maxOffset);
            newRange.setEnd(textNode, maxOffset);
            selection.removeAllRanges();
            selection.addRange(newRange);
          } catch (e) {
            // Ignore cursor restoration errors
          }
        }
      }
    }
  }, [value, content]);

  const handleContentChange = (e) => {
    if (isUpdatingRef.current) return;

    const newContent = e.target.innerHTML;
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }
  };

  const applyFormat = (command, value = null) => {
    // Save current selection
    const selection = window.getSelection();
    const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;

    if (range) {
      // Apply formatting to selected text
      document.execCommand(command, false, value);
    } else {
      // If no selection, focus and apply formatting for new text
      editorRef.current?.focus();
      document.execCommand(command, false, value);
    }

    // Update content after formatting
    isUpdatingRef.current = true;
    setTimeout(() => {
      const newContent = editorRef.current?.innerHTML || '';
      setContent(newContent);
      if (onChange) {
        onChange(newContent);
      }
      isUpdatingRef.current = false;
    }, 10);
  };

  const handleFontChange = (e) => {
    const font = e.target.value;
    setCurrentFont(font);
    applyFormat('fontName', font);
  };

  const handleColorChange = (e) => {
    const color = e.target.value;
    setCurrentColor(color);
    applyFormat('foreColor', color);
  };

  const handleFontSizeChange = (e) => {
    const fontSize = e.target.value;
    setCurrentFontSize(fontSize);

    const selection = window.getSelection();

    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      if (!range.collapsed) {
        // Create a span element with the new font size
        const span = document.createElement('span');
        span.style.fontSize = fontSize;

        try {
          // Try to surround the contents with the span
          range.surroundContents(span);

          // Restore selection to the newly wrapped content
          const newRange = document.createRange();
          newRange.selectNodeContents(span);
          selection.removeAllRanges();
          selection.addRange(newRange);

        } catch (error) {
          // If surroundContents fails (e.g., range spans multiple elements),
          // use a more robust approach
          try {
            const contents = range.extractContents();

            // If the extracted contents contain multiple nodes, wrap each text node
            const walker = document.createTreeWalker(
              contents,
              NodeFilter.SHOW_TEXT,
              null,
              false
            );

            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
              textNodes.push(node);
            }

            // If we have text nodes, wrap them
            if (textNodes.length > 0) {
              // Create a document fragment to hold our styled content
              const fragment = document.createDocumentFragment();

              // Clone the contents and apply styling
              const clonedContents = contents.cloneNode(true);
              const styledSpan = document.createElement('span');
              styledSpan.style.fontSize = fontSize;
              styledSpan.appendChild(clonedContents);
              fragment.appendChild(styledSpan);

              // Insert the styled content
              range.insertNode(fragment);

              // Select the newly inserted content
              const newRange = document.createRange();
              newRange.selectNodeContents(styledSpan);
              selection.removeAllRanges();
              selection.addRange(newRange);
            } else {
              // Fallback: just insert the original contents back
              range.insertNode(contents);
            }

          } catch (fallbackError) {
            console.warn('Font size application failed:', fallbackError);
            // Last resort: create a new span and insert it
            const fallbackSpan = document.createElement('span');
            fallbackSpan.style.fontSize = fontSize;
            fallbackSpan.textContent = selection.toString();

            try {
              range.deleteContents();
              range.insertNode(fallbackSpan);

              // Select the newly inserted span
              const newRange = document.createRange();
              newRange.selectNodeContents(fallbackSpan);
              selection.removeAllRanges();
              selection.addRange(newRange);
            } catch (finalError) {
              console.error('All font size application methods failed:', finalError);
            }
          }
        }
      } else {
        // No text selected - set font size for future typing
        editorRef.current?.focus();

        // Create a temporary span at cursor position for future typing
        const span = document.createElement('span');
        span.style.fontSize = fontSize;
        span.innerHTML = '&#8203;'; // Zero-width space to maintain cursor position

        try {
          range.insertNode(span);

          // Position cursor after the span
          const newRange = document.createRange();
          newRange.setStartAfter(span);
          newRange.setEndAfter(span);
          selection.removeAllRanges();
          selection.addRange(newRange);
        } catch (error) {
          console.warn('Could not set font size for future typing:', error);
        }
      }
    } else {
      // No selection at all - just focus the editor
      editorRef.current?.focus();
    }

    // Update content immediately
    isUpdatingRef.current = true;
    const newContent = editorRef.current?.innerHTML || '';
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }

    // Reset the updating flag after a brief delay
    setTimeout(() => {
      isUpdatingRef.current = false;
    }, 10);
  };

  const handleSelectionChange = () => {
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      setSelectedText(selection.toString());

      // Try to detect current formatting of selected text
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let parentElement = range.commonAncestorContainer;

        // If it's a text node, get its parent element
        if (parentElement.nodeType === Node.TEXT_NODE) {
          parentElement = parentElement.parentElement;
        }

        // Look for the closest element with a font-size style
        let currentElement = parentElement;
        while (currentElement && currentElement !== editorRef.current) {
          if (currentElement.style && currentElement.style.fontSize) {
            setCurrentFontSize(currentElement.style.fontSize);
            break;
          }

          // Also check computed style
          const computedStyle = window.getComputedStyle(currentElement);
          if (computedStyle.fontSize && computedStyle.fontSize !== 'inherit') {
            // Only update if it's one of our predefined sizes
            const fontSize = computedStyle.fontSize;
            const predefinedSizes = ['60px', '40px', '28px', '20px'];
            if (predefinedSizes.includes(fontSize)) {
              setCurrentFontSize(fontSize);
              break;
            }
          }

          currentElement = currentElement.parentElement;
        }
      }
    } else {
      setSelectedText('');
    }
  };

  return (
    <div className={`text-editor-container ${className}`} style={style}>
      {/* Editor Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-3">
          Rich Text Editor
        </h3>

        <div className="border border-gray-300 rounded-md overflow-hidden">
          {/* Custom Toolbar */}
          <div className="bg-gray-50 border-b border-gray-300 p-2 flex items-center gap-2 flex-wrap">
            {/* Bold Button */}
            <button
              type="button"
              onClick={() => applyFormat('bold')}
              className="px-3 py-1 bg-white border border-gray-300 rounded hover:bg-gray-100 font-bold"
              disabled={disabled}
              title="Bold (Ctrl+B)"
            >
              B
            </button>

            {/* Italic Button */}
            <button
              type="button"
              onClick={() => applyFormat('italic')}
              className="px-3 py-1 bg-white border border-gray-300 rounded hover:bg-gray-100 italic"
              disabled={disabled}
              title="Italic (Ctrl+I)"
            >
              I
            </button>

            {/* Font Family Selector */}
            <select
              value={currentFont}
              onChange={handleFontChange}
              disabled={disabled}
              className="px-2 py-1 bg-white border border-gray-300 rounded text-sm"
              title="Font Family"
            >
              <option value="Arial">Arial</option>
              <option value="Times New Roman">Times New Roman</option>
              <option value="Helvetica">Helvetica</option>
              <option value="Georgia">Georgia</option>
              <option value="Verdana">Verdana</option>
            </select>

            {/* Font Size Selector */}
            <select
              value={currentFontSize}
              onChange={handleFontSizeChange}
              disabled={disabled}
              className="px-2 py-1 bg-white border border-gray-300 rounded text-sm"
              title="Font Size"
            >
              <option value="60px">Large (60px)</option>
              <option value="40px">Medium (40px)</option>
              <option value="28px">Small (28px)</option>
              <option value="20px">Extra Small (20px)</option>
            </select>

            {/* Color Picker */}
            <div className="flex items-center gap-1">
              <label htmlFor="color-picker" className="text-sm text-gray-600">Color:</label>
              <input
                id="color-picker"
                type="color"
                value={currentColor}
                onChange={handleColorChange}
                disabled={disabled}
                className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
                title="Text Color"
              />
            </div>
          </div>

          {/* Editor Content */}
          <div
            ref={editorRef}
            contentEditable={!disabled}
            onInput={handleContentChange}
            onMouseUp={handleSelectionChange}
            onKeyUp={handleSelectionChange}
            onFocus={handleSelectionChange}
            onBlur={() => setSelectedText('')}
            className="p-4 bg-white focus:outline-none"
            style={{ minHeight }}
            data-placeholder={placeholder}
            suppressContentEditableWarning={true}
          />
        </div>
      </div>

      {/* Preview Section */}
      <div className="preview-section">
        <h3 className="text-lg font-medium text-gray-800 mb-3">
          Formatted Preview
        </h3>
        <div className="border border-gray-200 rounded-md p-4 min-h-[150px] bg-gray-50">
          {content ? (
            <div
              className="formatted-content"
              dangerouslySetInnerHTML={{ __html: content }}
            />
          ) : (
            <p className="text-gray-500 italic">
              Your formatted text will appear here...
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default TextEditor;
